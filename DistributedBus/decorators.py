"""
Decorators for service registration and event subscription.
"""

import inspect
import functools
from typing import Callable, Any, Dict, Set
from .rpc_framework import get_service_registry, ServiceRegistration


class ServiceProxy:
    """Proxy class that wraps service instances to automatically publish events."""

    def __init__(self, instance, service_name: str, bus=None):
        self._instance = instance
        self._service_name = service_name
        self._bus = bus
        self._mutating_methods = self._detect_mutating_methods()

    def _detect_mutating_methods(self) -> Set[str]:
        """Detect methods that likely mutate state (for auto event publishing)."""
        mutating_prefixes = ['add', 'create', 'insert', 'update', 'modify', 'edit',
                           'delete', 'remove', 'clear', 'set', 'put', 'save', 'write']
        mutating_methods = set()

        for method_name in dir(self._instance):
            if not method_name.startswith('_'):
                method = getattr(self._instance, method_name)
                if callable(method):
                    # Check if method name suggests mutation
                    if any(method_name.lower().startswith(prefix) for prefix in mutating_prefixes):
                        mutating_methods.add(method_name)

        return mutating_methods

    def _set_bus(self, bus):
        """Set the distributed bus for event publishing."""
        self._bus = bus

    def __getattr__(self, name):
        """Intercept method calls to automatically publish events."""
        attr = getattr(self._instance, name)

        if callable(attr) and name in self._mutating_methods:
            @functools.wraps(attr)
            def wrapper(*args, **kwargs):
                # Call the original method
                result = attr(*args, **kwargs)

                # Automatically publish event if bus is available
                if self._bus:
                    event_name = f"{self._service_name}_{name}"
                    event_data = {
                        "service": self._service_name,
                        "method": name,
                        "args": args,
                        "kwargs": kwargs,
                        "result": result
                    }
                    try:
                        self._bus.publish_event(event_name, event_data)
                    except Exception as e:
                        # Don't let event publishing break the original method
                        print(f"Warning: Failed to publish event {event_name}: {e}")

                return result

            return wrapper

        return attr

    def __setattr__(self, name, value):
        """Handle attribute setting."""
        if name.startswith('_'):
            # Internal attributes
            super().__setattr__(name, value)
        else:
            # Delegate to the wrapped instance
            setattr(self._instance, name, value)

    def __getattribute__(self, name):
        """Handle attribute access."""
        # Internal attributes
        if name.startswith('_') or name in ['_instance', '_service_name', '_bus', '_mutating_methods', '_set_bus', '_detect_mutating_methods']:
            return super().__getattribute__(name)

        # Delegate to the wrapped instance
        instance = super().__getattribute__('_instance')
        return getattr(instance, name)


def service(name: str):
    """
    Decorator to mark a class as an RPC service.
    This decorator is completely transparent - it doesn't require any changes to the original class.

    Args:
        name: The name of the service for discovery

    Example:
        @service(name="FileService")
        class FileService:
            def open(self, filename, mode):
                return open(filename, mode).read()
    """
    def decorator(cls):
        # Create a custom ServiceRegistration that creates ServiceProxy instances
        class ProxyServiceRegistration(ServiceRegistration):
            def create_instance(self, *args, **kwargs):
                if self.instance is None:
                    # Create the original instance
                    original_instance = self.service_class(*args, **kwargs)
                    # Wrap it in a ServiceProxy
                    self.instance = ServiceProxy(original_instance, name)
                return self.instance

        # Register the service
        registry = get_service_registry()
        registry[name] = ProxyServiceRegistration(name, cls)

        # Add service metadata to the original class
        cls._service_name = name
        cls._is_distributed_service = True
        cls._original_class = cls

        return cls

    return decorator


# Global registry for event subscribers
_event_subscribers = {}


def event_subscriber(event_name: str):
    """
    Decorator to mark a function as an event subscriber.
    
    Args:
        event_name: The name of the event to subscribe to
    
    Example:
        @event_subscriber("data_changed")
        def handle_data_change(event):
            print(f"Data changed: {event.data}")
    """
    def decorator(func: Callable):
        # Register the event subscriber
        if event_name not in _event_subscribers:
            _event_subscribers[event_name] = []
        
        _event_subscribers[event_name].append(func)
        
        # Add metadata to the function
        func._event_name = event_name
        func._is_event_subscriber = True
        
        return func
    
    return decorator


def get_event_subscribers():
    """Get the global event subscribers registry."""
    return _event_subscribers


class EventData:
    """Container for event data."""
    
    def __init__(self, event_name: str, data: Any, timestamp: float, source_node: str = None):
        self.event_name = event_name
        self.data = data
        self.timestamp = timestamp
        self.source_node = source_node
