"""
RPC Framework with annotation-based service registration and secure communication.
"""

import socket
import threading
import time
from xmlrpc.server import SimpleXMLRPCServer
from xmlrpc.client import ServerProxy
import ssl
from typing import Dict, Any, Optional
import inspect

from .personal_ca import PersonalCA
from .service_discovery import ServiceDiscovery, EncryptedServiceInfo


class SecureXMLRPCServer(SimpleXMLRPCServer):
    """XML-RPC server with SSL support."""
    
    def __init__(self, addr, personal_ca: PersonalCA, *args, **kwargs):
        super().__init__(addr, *args, **kwargs)
        self.personal_ca = personal_ca
        
        # Wrap socket with SSL
        context = personal_ca.get_ssl_context(server=True)
        self.socket = context.wrap_socket(self.socket, server_side=True)


class RPCServiceProxy:
    """Proxy for remote RPC services."""
    
    def __init__(self, service_info: EncryptedServiceInfo, personal_ca: PersonalCA):
        self.service_info = service_info
        self.personal_ca = personal_ca
        self._proxy = None
        self._connect()
    
    def _connect(self):
        """Connect to the remote service."""
        try:
            # Create SSL context for client
            context = self.personal_ca.get_ssl_context(server=False)
            
            # Create XML-RPC proxy with SSL
            url = f"https://{self.service_info.address}:{self.service_info.port}"
            self._proxy = ServerProxy(url, context=context)
            
        except Exception as e:
            print(f"Failed to connect to service {self.service_info.service_name}: {e}")
            self._proxy = None
    
    def __getattr__(self, name):
        """Forward method calls to the remote service."""
        if self._proxy is None:
            self._connect()
        
        if self._proxy is None:
            raise ConnectionError(f"Cannot connect to service {self.service_info.service_name}")
        
        return getattr(self._proxy, name)


class RPCServer:
    """RPC server for hosting services."""
    
    def __init__(self, personal_ca: PersonalCA, service_discovery: ServiceDiscovery):
        self.personal_ca = personal_ca
        self.service_discovery = service_discovery
        self.servers: Dict[str, SecureXMLRPCServer] = {}
        self.server_threads: Dict[str, threading.Thread] = {}
        self.services: Dict[str, Any] = {}
    
    def register_service(self, service_name: str, service_instance: Any):
        """Register a service instance."""
        try:
            # Find an available port
            port = self._find_available_port()

            # Create SSL-enabled XML-RPC server
            server = SecureXMLRPCServer(
                ("0.0.0.0", port),
                self.personal_ca,
                allow_none=True,
                use_builtin_types=True
            )

            # If this is a ServiceProxy, set the bus reference
            if hasattr(service_instance, '_set_bus'):
                service_instance._set_bus(self.service_discovery.personal_ca.bus if hasattr(self.service_discovery.personal_ca, 'bus') else None)

            # Register service methods
            self._register_service_methods(server, service_instance)

            # Start server in a separate thread
            server_thread = threading.Thread(
                target=server.serve_forever,
                daemon=True
            )
            server_thread.start()

            # Store server and thread references
            self.servers[service_name] = server
            self.server_threads[service_name] = server_thread
            self.services[service_name] = service_instance

            # Register with service discovery (service discovery handles unique naming)
            self.service_discovery.register_service(service_name, port)

            print(f"RPC service '{service_name}' started on port {port}")

        except Exception as e:
            print(f"Failed to register RPC service '{service_name}': {e}")
    
    def _register_service_methods(self, server: SecureXMLRPCServer, service_instance: Any):
        """Register all public methods of a service instance."""
        for method_name in dir(service_instance):
            if not method_name.startswith('_'):
                method = getattr(service_instance, method_name)
                if callable(method):
                    server.register_function(method, method_name)
    
    def _find_available_port(self) -> int:
        """Find an available port for the RPC server."""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            return s.getsockname()[1]
    
    def unregister_service(self, service_name: str):
        """Unregister a service."""
        if service_name in self.servers:
            # Shutdown server
            self.servers[service_name].shutdown()
            del self.servers[service_name]
            del self.server_threads[service_name]
            del self.services[service_name]
            
            # Unregister from service discovery
            self.service_discovery.unregister_service(service_name)
            
            print(f"RPC service '{service_name}' unregistered")
    
    def close(self):
        """Close all RPC servers."""
        for service_name in list(self.servers.keys()):
            self.unregister_service(service_name)


class RPCClient:
    """RPC client for accessing remote services."""
    
    def __init__(self, personal_ca: PersonalCA, service_discovery: ServiceDiscovery):
        self.personal_ca = personal_ca
        self.service_discovery = service_discovery
        self.proxies: Dict[str, RPCServiceProxy] = {}
    
    def lookup_service(self, service_name: str, timeout: float = 5.0) -> Optional[RPCServiceProxy]:
        """Look up a service and return a proxy."""
        # Check if we already have a proxy
        if service_name in self.proxies:
            return self.proxies[service_name]
        
        # Try to find the service
        start_time = time.time()
        user_id = self.personal_ca.get_user_id()

        while time.time() - start_time < timeout:
            # First try exact match
            service_info = self.service_discovery.lookup_service(service_name)
            if service_info:
                proxy = RPCServiceProxy(service_info, self.personal_ca)
                self.proxies[service_name] = proxy
                return proxy

            # Look for services with matching service name from any user
            all_services = self.service_discovery.list_services()

            # Prioritize services from the same user (fast self-discovery)
            same_user_services = []
            other_user_services = []

            for discovered_name, discovered_info in all_services.items():
                if discovered_info.service_name == service_name:
                    if hasattr(discovered_info, 'user_id') and discovered_info.user_id == user_id:
                        same_user_services.append(discovered_info)
                    else:
                        other_user_services.append(discovered_info)

            # Try same user services first (self-discovery)
            for service_info in same_user_services:
                proxy = RPCServiceProxy(service_info, self.personal_ca)
                self.proxies[service_name] = proxy
                return proxy

            # Then try other user services
            for service_info in other_user_services:
                proxy = RPCServiceProxy(service_info, self.personal_ca)
                self.proxies[service_name] = proxy
                return proxy

            time.sleep(0.1)
        
        return None
    
    def clear_proxy_cache(self):
        """Clear the proxy cache."""
        self.proxies.clear()


# Global registry for services marked with @service decorator
_service_registry = {}


def get_service_registry():
    """Get the global service registry."""
    return _service_registry


class ServiceRegistration:
    """Information about a registered service."""
    
    def __init__(self, name: str, service_class: type):
        self.name = name
        self.service_class = service_class
        self.instance = None
    
    def create_instance(self, *args, **kwargs):
        """Create an instance of the service."""
        if self.instance is None:
            self.instance = self.service_class(*args, **kwargs)
        return self.instance
