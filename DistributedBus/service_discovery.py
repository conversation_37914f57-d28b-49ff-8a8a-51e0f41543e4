"""
Service Discovery using encrypted mDNS for automatic node discovery.
"""

import json
import socket
import threading
import time
from typing import Dict, Callable, Optional
from zeroconf import ServiceBrowser, ServiceListener, Zeroconf, ServiceInfo
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

from .personal_ca import PersonalCA


class EncryptedServiceInfo:
    """Encrypted service information for secure discovery."""
    
    def __init__(self, service_name: str, address: str, port: int, ca_fingerprint: str):
        self.service_name = service_name
        self.address = address
        self.port = port
        self.ca_fingerprint = ca_fingerprint
        self.timestamp = time.time()


class ServiceDiscovery:
    """Service discovery using encrypted mDNS."""
    
    def __init__(self, personal_ca: PersonalCA):
        self.personal_ca = personal_ca
        self.zeroconf = Zeroconf()
        self.services: Dict[str, EncryptedServiceInfo] = {}
        self.service_listeners: Dict[str, Callable] = {}
        self.browser = None
        self.registered_services = []
        self._lock = threading.Lock()
        
        # Create encryption key from CA fingerprint
        ca_fingerprint = personal_ca.get_ca_fingerprint()
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'distributed_bus_salt',
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(ca_fingerprint.encode()))
        self.cipher = Fernet(key)
        
        self._start_browser()
    
    def _start_browser(self):
        """Start the service browser."""
        listener = ServiceDiscoveryListener(self)
        self.browser = ServiceBrowser(self.zeroconf, "_distributed-bus._tcp.local.", listener)
    
    def register_service(self, service_name: str, port: int):
        """Register a service for discovery."""
        try:
            # Get local IP address
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            
            # Create service info to encrypt
            service_data = {
                "service_name": service_name,
                "address": local_ip,
                "port": port,
                "ca_fingerprint": self.personal_ca.get_ca_fingerprint()
            }
            
            # Encrypt service data
            encrypted_data = self.cipher.encrypt(json.dumps(service_data).encode())
            
            # Create mDNS service info with encrypted data
            service_info = ServiceInfo(
                "_distributed-bus._tcp.local.",
                f"{service_name}._distributed-bus._tcp.local.",
                addresses=[socket.inet_aton(local_ip)],
                port=port,
                properties={
                    "encrypted_data": base64.b64encode(encrypted_data).decode(),
                    "version": "1.0"
                },
                server=f"{socket.gethostname()}.local."
            )
            
            self.zeroconf.register_service(service_info)
            self.registered_services.append(service_info)
            
            print(f"Registered service: {service_name} on {local_ip}:{port}")
            
        except Exception as e:
            print(f"Failed to register service {service_name}: {e}")
    
    def unregister_service(self, service_name: str):
        """Unregister a service."""
        for service_info in self.registered_services[:]:
            if service_info.name.startswith(service_name):
                self.zeroconf.unregister_service(service_info)
                self.registered_services.remove(service_info)
                print(f"Unregistered service: {service_name}")
                break
    
    def lookup_service(self, service_name: str) -> Optional[EncryptedServiceInfo]:
        """Look up a service by name."""
        with self._lock:
            return self.services.get(service_name)
    
    def list_services(self) -> Dict[str, EncryptedServiceInfo]:
        """List all discovered services."""
        with self._lock:
            return self.services.copy()
    
    def add_service_listener(self, service_name: str, callback: Callable):
        """Add a listener for when a specific service is discovered."""
        self.service_listeners[service_name] = callback
    
    def _add_service(self, service_info: EncryptedServiceInfo):
        """Add a discovered service."""
        with self._lock:
            self.services[service_info.service_name] = service_info
            
            # Notify listeners
            if service_info.service_name in self.service_listeners:
                try:
                    self.service_listeners[service_info.service_name](service_info)
                except Exception as e:
                    print(f"Error in service listener: {e}")
    
    def _remove_service(self, service_name: str):
        """Remove a service."""
        with self._lock:
            if service_name in self.services:
                del self.services[service_name]
                print(f"Service removed: {service_name}")
    
    def close(self):
        """Close the service discovery."""
        for service_info in self.registered_services:
            self.zeroconf.unregister_service(service_info)
        
        if self.browser:
            self.browser.cancel()
        
        self.zeroconf.close()


class ServiceDiscoveryListener(ServiceListener):
    """Listener for mDNS service discovery events."""
    
    def __init__(self, discovery: ServiceDiscovery):
        self.discovery = discovery
    
    def add_service(self, zeroconf: Zeroconf, type_: str, name: str):
        """Called when a service is discovered."""
        info = zeroconf.get_service_info(type_, name)
        if info and info.properties:
            try:
                # Get encrypted data from properties
                encrypted_data_b64 = info.properties.get(b"encrypted_data")
                if not encrypted_data_b64:
                    return
                
                encrypted_data = base64.b64decode(encrypted_data_b64.decode())
                
                # Decrypt service data
                decrypted_data = self.discovery.cipher.decrypt(encrypted_data)
                service_data = json.loads(decrypted_data.decode())
                
                # Verify CA fingerprint matches
                if service_data["ca_fingerprint"] != self.discovery.personal_ca.get_ca_fingerprint():
                    return  # Not from our personal CA
                
                # Create service info
                service_info = EncryptedServiceInfo(
                    service_data["service_name"],
                    service_data["address"],
                    service_data["port"],
                    service_data["ca_fingerprint"]
                )
                
                self.discovery._add_service(service_info)
                print(f"Discovered service: {service_info.service_name} at {service_info.address}:{service_info.port}")
                
            except Exception as e:
                # Ignore services we can't decrypt (not from our personal CA)
                pass
    
    def remove_service(self, zeroconf: Zeroconf, type_: str, name: str):
        """Called when a service is removed."""
        service_name = name.split('.')[0]
        self.discovery._remove_service(service_name)
    
    def update_service(self, zeroconf: Zeroconf, type_: str, name: str):
        """Called when a service is updated."""
        # Treat as add for simplicity
        self.add_service(zeroconf, type_, name)
