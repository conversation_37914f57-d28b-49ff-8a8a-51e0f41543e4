#!/usr/bin/env python3
"""
Complete demo of the Distributed Bus Framework.
This script demonstrates all major features of the framework.
"""

import sys
import time
import threading
from DistributedBus import DistributedBus, service, event_subscriber


# Demo Services
@service(name="CalculatorService")
class CalculatorService:
    """Simple calculator service."""
    
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def multiply(self, a, b):
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result
    
    def get_history(self):
        return self.history.copy()


@service(name="ChatService")
class ChatService:
    """Simple chat service."""
    
    def __init__(self):
        self.messages = []
        self.bus = None
    
    def set_bus(self, bus):
        self.bus = bus
    
    def send_message(self, user, message):
        msg = {
            "user": user,
            "message": message,
            "timestamp": time.time()
        }
        self.messages.append(msg)
        
        # Publish event
        if self.bus:
            self.bus.publish_event("new_message", msg)
        
        return True
    
    def get_messages(self):
        return self.messages.copy()


# Event Handlers
@event_subscriber("new_message")
def handle_new_message(event):
    """Handle new chat messages."""
    msg = event.data
    print(f"💬 [{msg['user']}]: {msg['message']}")


@event_subscriber("calculation_done")
def handle_calculation(event):
    """Handle calculation events."""
    calc = event.data
    print(f"🧮 Calculation: {calc['expression']} = {calc['result']}")


def demo_rpc_services():
    """Demonstrate RPC service usage."""
    print("\n" + "="*50)
    print("🚀 RPC SERVICES DEMO")
    print("="*50)
    
    bus = DistributedBus().start()
    
    # Look up calculator service
    print("📊 Looking up CalculatorService...")
    calc = bus.lookup("CalculatorService", timeout=3.0)
    
    if calc:
        print("✅ CalculatorService found!")
        
        # Perform calculations
        result1 = calc.add(10, 5)
        print(f"   calc.add(10, 5) = {result1}")
        
        result2 = calc.multiply(7, 8)
        print(f"   calc.multiply(7, 8) = {result2}")
        
        # Publish calculation events
        bus.publish_event("calculation_done", {
            "expression": "10 + 5",
            "result": result1
        })
        
        bus.publish_event("calculation_done", {
            "expression": "7 * 8", 
            "result": result2
        })
        
        # Get history
        history = calc.get_history()
        print(f"   History: {history}")
        
    else:
        print("❌ CalculatorService not found")
    
    time.sleep(0.5)  # Let events process
    bus.close()


def demo_chat_service():
    """Demonstrate chat service with events."""
    print("\n" + "="*50)
    print("💬 CHAT SERVICE DEMO")
    print("="*50)
    
    bus = DistributedBus().start()
    
    # Look up chat service
    print("🔍 Looking up ChatService...")
    chat = bus.lookup("ChatService", timeout=3.0)
    
    if chat:
        print("✅ ChatService found!")
        
        # Set bus reference for event publishing
        chat_service_instance = None
        for service_name, service_instance in bus.rpc_server.services.items():
            if service_name == "ChatService":
                service_instance.set_bus(bus)
                break
        
        # Send some messages
        chat.send_message("Alice", "Hello everyone!")
        chat.send_message("Bob", "Hi Alice! How are you?")
        chat.send_message("Charlie", "Good morning!")
        
        time.sleep(0.5)  # Let events process
        
        # Get all messages
        messages = chat.get_messages()
        print(f"\n📝 Chat history ({len(messages)} messages):")
        for msg in messages:
            timestamp = time.strftime("%H:%M:%S", time.localtime(msg["timestamp"]))
            print(f"   [{timestamp}] {msg['user']}: {msg['message']}")
    
    else:
        print("❌ ChatService not found")
    
    bus.close()


def demo_multi_node_simulation():
    """Simulate multiple nodes communicating."""
    print("\n" + "="*50)
    print("🌐 MULTI-NODE SIMULATION")
    print("="*50)
    
    # This simulates what would happen with multiple physical nodes
    print("🖥️  Simulating Node 1 (Service Provider)...")
    bus1 = DistributedBus().start()
    
    print("📱 Simulating Node 2 (Service Consumer)...")
    bus2 = DistributedBus().start()
    
    # Node 2 looks up services from Node 1
    print("🔍 Node 2 looking for services...")
    calc = bus2.lookup("CalculatorService", timeout=3.0)
    
    if calc:
        print("✅ Node 2 found CalculatorService from Node 1!")
        result = calc.add(100, 200)
        print(f"   Remote calculation: 100 + 200 = {result}")
        
        # Publish event from Node 2
        bus2.publish_event("calculation_done", {
            "expression": "100 + 200",
            "result": result,
            "node": "Node 2"
        })
    
    time.sleep(0.5)  # Let events process
    
    # Show node information
    print("\n📊 Node Information:")
    node1_info = bus1.get_node_info()
    node2_info = bus2.get_node_info()
    
    print(f"   Node 1 - Local Services: {node1_info['local_services']}")
    print(f"   Node 1 - Discovered: {node1_info['discovered_services']}")
    print(f"   Node 2 - Local Services: {node2_info['local_services']}")
    print(f"   Node 2 - Discovered: {node2_info['discovered_services']}")
    
    bus1.close()
    bus2.close()


def demo_event_system():
    """Demonstrate the event system."""
    print("\n" + "="*50)
    print("📡 EVENT SYSTEM DEMO")
    print("="*50)
    
    bus = DistributedBus().start()
    
    # Manual event subscription
    received_events = []
    
    def custom_handler(event):
        received_events.append(event)
        print(f"🎯 Custom handler received: {event.event_name}")
    
    bus.subscribe("custom_event", custom_handler)
    
    # Publish various events
    print("📤 Publishing events...")
    
    bus.publish_event("custom_event", {"type": "test", "value": 123})
    bus.publish_event("system_event", {"status": "running", "uptime": 3600})
    bus.publish_event("user_action", {"action": "login", "user": "demo_user"})
    
    time.sleep(0.5)  # Let events process
    
    print(f"✅ Custom handler received {len(received_events)} events")
    
    bus.close()


def main():
    """Main demo function."""
    print("🎉 DISTRIBUTED BUS FRAMEWORK DEMO")
    print("=" * 60)
    print("This demo showcases the key features of the framework:")
    print("• Service Discovery and RPC")
    print("• Event Publishing and Subscribing") 
    print("• Multi-Node Communication")
    print("• Secure Personal CA Authentication")
    
    try:
        # Run all demos
        demo_rpc_services()
        demo_chat_service()
        demo_event_system()
        demo_multi_node_simulation()
        
        print("\n" + "="*60)
        print("🎊 DEMO COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("\n📚 Next Steps:")
        print("• Try the memo application: python examples/memo_app/run_demo.py")
        print("• Run tests: python tests/test_distributed_bus.py")
        print("• Read documentation: docs/GETTING_STARTED.md")
        print("• Build your own MPN application!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
