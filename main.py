"""
Main entry point for the Distributed Memo Android application
"""

import os
import sys

# Ensure the current directory is in the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Import and run the distributed memo app
from memo_app_distributed import DistributedMemoApp

if __name__ == '__main__':
    DistributedMemoApp().run()
