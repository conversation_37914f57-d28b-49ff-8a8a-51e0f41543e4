#!/bin/bash
"""
Android部署脚本
将DistributedBus框架和应用部署到Android设备
"""

echo "🚀 Deploying DistributedBus to Android..."

# 检查ADB连接
if ! adb devices | grep -q "device$"; then
    echo "❌ No Android device connected via ADB"
    exit 1
fi

echo "✅ Android device detected"

# 创建Android目录结构
echo "📁 Creating directory structure on Android..."
adb shell "mkdir -p /sdcard/DistributedBus"
adb shell "mkdir -p /sdcard/DistributedBus/DistributedBus"
adb shell "mkdir -p /sdcard/DistributedBus/examples"

# 推送框架文件
echo "📦 Pushing framework files..."
adb push DistributedBus/ /sdcard/DistributedBus/
adb push examples/cross_platform_memo/ /sdcard/DistributedBus/examples/
adb push examples/transparent_memo/ /sdcard/DistributedBus/examples/
adb push requirements.txt /sdcard/DistributedBus/

# 创建Android启动脚本
echo "📝 Creating Android startup script..."
cat > android_start.py << 'EOF'
#!/usr/bin/env python3
"""
Android启动脚本
在Android设备上运行DistributedBus框架
"""

import sys
import os

# 添加框架路径
sys.path.insert(0, '/sdcard/DistributedBus')
sys.path.insert(0, '/sdcard/DistributedBus/examples/transparent_memo')

print("🤖 Starting DistributedBus on Android...")
print("📱 Platform: Android")

try:
    # 测试框架导入
    from DistributedBus import DistributedBus, service
    print("✅ DistributedBus framework imported successfully")
    
    # 测试透明服务
    from memo_manager import MemoManager
    print("✅ MemoManager imported successfully")
    
    # 启动分布式总线
    print("🚀 Starting DistributedBus...")
    bus = DistributedBus().start()
    
    # 查找服务
    print("🔍 Looking for MemoService...")
    memo_service = bus.lookup("MemoService", timeout=3.0)
    
    if memo_service:
        print("✅ MemoService found!")
        
        # 测试基本操作
        print("📝 Testing basic operations...")
        memo_id = memo_service.add_memo("Android Test", "Created on Android device", ["android", "test"])
        print(f"   Added memo: {memo_id}")
        
        memos = memo_service.get_all_memos()
        print(f"   Total memos: {len(memos)}")
        
        # 显示所有备忘录
        for memo in memos:
            print(f"   - {memo['title']}: {memo['content'][:50]}...")
    
    else:
        print("⚠️  MemoService not found, framework is running but no service available")
    
    print("\n🎉 Android test completed successfully!")
    print("📱 DistributedBus is working on Android!")
    
    # 保持运行一段时间以便测试
    import time
    print("⏰ Keeping service running for 30 seconds...")
    time.sleep(30)
    
    bus.close()
    print("👋 Android test finished")

except Exception as e:
    print(f"❌ Android test failed: {e}")
    import traceback
    traceback.print_exc()
EOF

# 推送启动脚本
adb push android_start.py /sdcard/DistributedBus/

# 创建简化的Android应用
echo "📱 Creating simplified Android app..."
cat > android_simple_memo.py << 'EOF'
#!/usr/bin/env python3
"""
简化的Android备忘录应用
不依赖Kivy，使用控制台界面
"""

import sys
import os
import time

# 添加框架路径
sys.path.insert(0, '/sdcard/DistributedBus')
sys.path.insert(0, '/sdcard/DistributedBus/examples/transparent_memo')

from DistributedBus import DistributedBus
from memo_manager import MemoManager

class SimpleMemoApp:
    """简化的备忘录应用"""
    
    def __init__(self):
        print("🤖 Starting Simple Memo App on Android...")
        self.bus = DistributedBus().start()
        
        # 连接到服务
        self.memo_service = self.bus.lookup("MemoService", timeout=5.0)
        if not self.memo_service:
            print("⚠️  Creating local MemoService...")
            local_manager = MemoManager("android_memos.json")
            self.bus.register_service("MemoService", local_manager)
            self.memo_service = self.bus.lookup("MemoService")
        
        print("✅ Connected to MemoService")
    
    def run(self):
        """运行应用"""
        while True:
            self.show_menu()
            choice = input("请选择操作 (1-5): ").strip()
            
            if choice == '1':
                self.add_memo()
            elif choice == '2':
                self.list_memos()
            elif choice == '3':
                self.update_memo()
            elif choice == '4':
                self.delete_memo()
            elif choice == '5':
                print("👋 退出应用")
                break
            else:
                print("❌ 无效选择")
    
    def show_menu(self):
        """显示菜单"""
        print("\n" + "="*40)
        print("📝 Android备忘录应用")
        print("="*40)
        print("1. 添加备忘录")
        print("2. 查看备忘录")
        print("3. 更新备忘录")
        print("4. 删除备忘录")
        print("5. 退出")
        print("="*40)
    
    def add_memo(self):
        """添加备忘录"""
        print("\n📝 添加备忘录")
        title = input("标题: ").strip()
        content = input("内容: ").strip()
        
        if title:
            memo_id = self.memo_service.add_memo(title, content, ["android"])
            print(f"✅ 已添加备忘录: {title}")
        else:
            print("❌ 标题不能为空")
    
    def list_memos(self):
        """列出备忘录"""
        print("\n📋 备忘录列表")
        memos = self.memo_service.get_all_memos()
        
        if not memos:
            print("📭 暂无备忘录")
            return
        
        for i, memo in enumerate(memos, 1):
            created = time.strftime("%Y-%m-%d %H:%M", time.localtime(memo['created_at']))
            print(f"{i}. {memo['title']}")
            print(f"   内容: {memo['content']}")
            print(f"   创建: {created}")
            print(f"   ID: {memo['id'][:8]}...")
            print()
    
    def update_memo(self):
        """更新备忘录"""
        print("\n✏️  更新备忘录")
        memos = self.memo_service.get_all_memos()
        
        if not memos:
            print("📭 暂无备忘录")
            return
        
        # 显示备忘录列表
        for i, memo in enumerate(memos, 1):
            print(f"{i}. {memo['title']}")
        
        try:
            index = int(input("选择要更新的备忘录 (序号): ")) - 1
            if 0 <= index < len(memos):
                memo = memos[index]
                print(f"当前标题: {memo['title']}")
                print(f"当前内容: {memo['content']}")
                
                new_title = input("新标题 (回车保持不变): ").strip()
                new_content = input("新内容 (回车保持不变): ").strip()
                
                title = new_title if new_title else memo['title']
                content = new_content if new_content else memo['content']
                
                success = self.memo_service.update_memo(memo['id'], title, content)
                if success:
                    print("✅ 更新成功")
                else:
                    print("❌ 更新失败")
            else:
                print("❌ 无效序号")
        except ValueError:
            print("❌ 请输入有效数字")
    
    def delete_memo(self):
        """删除备忘录"""
        print("\n🗑️  删除备忘录")
        memos = self.memo_service.get_all_memos()
        
        if not memos:
            print("📭 暂无备忘录")
            return
        
        # 显示备忘录列表
        for i, memo in enumerate(memos, 1):
            print(f"{i}. {memo['title']}")
        
        try:
            index = int(input("选择要删除的备忘录 (序号): ")) - 1
            if 0 <= index < len(memos):
                memo = memos[index]
                confirm = input(f"确定删除 '{memo['title']}' ? (y/N): ").strip().lower()
                
                if confirm == 'y':
                    success = self.memo_service.delete_memo(memo['id'])
                    if success:
                        print("✅ 删除成功")
                    else:
                        print("❌ 删除失败")
                else:
                    print("❌ 取消删除")
            else:
                print("❌ 无效序号")
        except ValueError:
            print("❌ 请输入有效数字")
    
    def close(self):
        """关闭应用"""
        self.bus.close()

if __name__ == "__main__":
    try:
        app = SimpleMemoApp()
        app.run()
        app.close()
    except KeyboardInterrupt:
        print("\n👋 应用被中断")
    except Exception as e:
        print(f"❌ 应用错误: {e}")
        import traceback
        traceback.print_exc()
EOF

# 推送简化应用
adb push android_simple_memo.py /sdcard/DistributedBus/

echo "✅ Deployment completed!"
echo ""
echo "📱 To test on Android device:"
echo "1. Install Termux from F-Droid or Google Play"
echo "2. In Termux, run: pkg install python"
echo "3. Install dependencies: pip install cryptography zeroconf"
echo "4. Run framework test: python /sdcard/DistributedBus/android_start.py"
echo "5. Run memo app: python /sdcard/DistributedBus/android_simple_memo.py"
echo ""
echo "🔗 For multi-device testing:"
echo "1. Start the memo service on one device"
echo "2. Run the memo app on another device"
echo "3. They should automatically discover and sync!"

# 清理临时文件
rm -f android_start.py android_simple_memo.py

echo "🎉 Android deployment script completed!"
