"""
Cross-platform Memo Application GUI using Kivy
Works on both Android and macOS
"""

import os
import sys
from datetime import datetime
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.scrollview import ScrollView
from kivy.uix.gridlayout import GridLayout
from kivy.uix.popup import Popup
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.clock import Clock

# CHANGE 1: Add DistributedBus to path and import distributed components
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
from DistributedBus import DistributedBus, service
from memo_manager import MemoManager  # This already has @service annotation


class MemoListScreen(Screen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # CHANGE 2: Initialize distributed system instead of direct MemoManager
        self.memo_manager = None
        self.bus = None
        self.build_ui()
        self.setup_distributed_system()

    def setup_distributed_system(self):
        """CHANGE 3: Setup distributed system instead of direct instantiation"""
        self.bus = DistributedBus().start()
        self.memo_manager = self.bus.lookup("MemoService")
        print("DistributedBus initialized successfully")
        print(f"MemoService obtained: {self.memo_manager is not None}")
        Clock.schedule_once(lambda dt: self.refresh_memo_list(), 1)

    def build_ui(self):
        main_layout = BoxLayout(orientation='vertical', padding=10, spacing=10)

        # Title
        title = Label(text='Memo App', size_hint_y=None, height=50, font_size=24)
        main_layout.add_widget(title)

        # Search bar
        search_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=40)
        self.search_input = TextInput(hint_text='Search memos...', multiline=False)
        search_btn = Button(text='Search', size_hint_x=None, width=80)
        search_btn.bind(on_press=self.search_memos)
        search_layout.add_widget(self.search_input)
        search_layout.add_widget(search_btn)
        main_layout.add_widget(search_layout)

        # Memo list
        self.memo_scroll = ScrollView()
        self.memo_list = GridLayout(cols=1, size_hint_y=None, spacing=5)
        self.memo_list.bind(minimum_height=self.memo_list.setter('height'))
        self.memo_scroll.add_widget(self.memo_list)
        main_layout.add_widget(self.memo_scroll)

        # Buttons
        button_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=50, spacing=10)

        add_btn = Button(text='Add Memo')
        add_btn.bind(on_press=self.show_add_memo)

        refresh_btn = Button(text='Refresh')
        refresh_btn.bind(on_press=lambda x: self.refresh_memo_list())

        clear_btn = Button(text='Clear All')
        clear_btn.bind(on_press=self.clear_all_memos)

        button_layout.add_widget(add_btn)
        button_layout.add_widget(refresh_btn)
        button_layout.add_widget(clear_btn)

        main_layout.add_widget(button_layout)
        self.add_widget(main_layout)

    def refresh_memo_list(self):
        if not self.memo_manager:
            return

        self.memo_list.clear_widgets()
        memos = self.memo_manager.get_all_memos()

        if not memos:
            no_memos = Label(text='No memos yet. Tap "Add Memo" to create one!',
                           size_hint_y=None, height=50)
            self.memo_list.add_widget(no_memos)
            return

        # Sort by updated_at descending
        memos.sort(key=lambda x: x.get('updated_at', 0), reverse=True)

        for memo in memos:
            memo_widget = self.create_memo_widget(memo)
            self.memo_list.add_widget(memo_widget)
    
    def create_memo_widget(self, memo):
        memo_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=120, 
                               padding=5, spacing=5)
        
        # Title and date
        header_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=30)
        title_label = Label(text=memo['title'], font_size=16, bold=True, 
                          text_size=(None, None), halign='left')
        
        # Format date
        date_str = datetime.fromtimestamp(memo['updated_at']).strftime('%m/%d %H:%M')
        date_label = Label(text=date_str, size_hint_x=None, width=80, font_size=12)
        
        header_layout.add_widget(title_label)
        header_layout.add_widget(date_label)
        memo_layout.add_widget(header_layout)
        
        # Content preview
        content_preview = memo['content'][:100] + ('...' if len(memo['content']) > 100 else '')
        content_label = Label(text=content_preview, text_size=(None, None), 
                            font_size=12, halign='left')
        memo_layout.add_widget(content_label)
        
        # Buttons
        btn_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=30, spacing=5)
        
        edit_btn = Button(text='Edit', size_hint_x=None, width=60)
        edit_btn.bind(on_press=lambda x, m=memo: self.show_edit_memo(m))
        
        delete_btn = Button(text='Delete', size_hint_x=None, width=60)
        delete_btn.bind(on_press=lambda x, m=memo: self.delete_memo(m))
        
        btn_layout.add_widget(edit_btn)
        btn_layout.add_widget(delete_btn)
        btn_layout.add_widget(Label())  # Spacer
        
        memo_layout.add_widget(btn_layout)
        
        return memo_layout
    
    def search_memos(self, instance):
        query = self.search_input.text.strip()
        if query:
            memos = self.memo_manager.search_memos(query)
        else:
            memos = self.memo_manager.get_all_memos()

        self.memo_list.clear_widgets()

        if not memos:
            no_results = Label(text='No memos found.', size_hint_y=None, height=50)
            self.memo_list.add_widget(no_results)
            return

        memos.sort(key=lambda x: x.get('updated_at', 0), reverse=True)
        for memo in memos:
            memo_widget = self.create_memo_widget(memo)
            self.memo_list.add_widget(memo_widget)

    def show_add_memo(self, instance):
        self.manager.get_screen('edit').setup_for_add()
        self.manager.current = 'edit'

    def show_edit_memo(self, memo):
        self.manager.get_screen('edit').setup_for_edit(memo)
        self.manager.current = 'edit'

    def delete_memo(self, memo):
        self.memo_manager.delete_memo(memo['id'])
        self.refresh_memo_list()

    def clear_all_memos(self, instance):
        popup_layout = BoxLayout(orientation='vertical', spacing=10)
        popup_layout.add_widget(Label(text='Are you sure you want to clear all memos?'))

        btn_layout = BoxLayout(orientation='horizontal', spacing=10, size_hint_y=None, height=40)
        yes_btn = Button(text='Yes')
        no_btn = Button(text='No')

        popup = Popup(title='Confirm Clear All', content=popup_layout, size_hint=(0.8, 0.4))

        yes_btn.bind(on_press=lambda x: self.confirm_clear_all(popup))
        no_btn.bind(on_press=popup.dismiss)

        btn_layout.add_widget(yes_btn)
        btn_layout.add_widget(no_btn)
        popup_layout.add_widget(btn_layout)

        popup.open()

    def confirm_clear_all(self, popup):
        self.memo_manager.clear_all_memos()
        self.refresh_memo_list()
        popup.dismiss()


class MemoEditScreen(Screen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.memo_manager = None
        self.current_memo = None
        self.build_ui()
    
    def build_ui(self):
        main_layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # Title
        self.screen_title = Label(text='Add Memo', size_hint_y=None, height=50, font_size=20)
        main_layout.add_widget(self.screen_title)
        
        # Title input
        main_layout.add_widget(Label(text='Title:', size_hint_y=None, height=30, halign='left'))
        self.title_input = TextInput(multiline=False, size_hint_y=None, height=40)
        main_layout.add_widget(self.title_input)
        
        # Content input
        main_layout.add_widget(Label(text='Content:', size_hint_y=None, height=30, halign='left'))
        self.content_input = TextInput(multiline=True)
        main_layout.add_widget(self.content_input)
        
        # Tags input
        main_layout.add_widget(Label(text='Tags (comma separated):', size_hint_y=None, height=30, halign='left'))
        self.tags_input = TextInput(multiline=False, size_hint_y=None, height=40)
        main_layout.add_widget(self.tags_input)
        
        # Buttons
        btn_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=50, spacing=10)

        save_btn = Button(text='Save')
        save_btn.bind(on_press=self.save_memo)

        cancel_btn = Button(text='Cancel')
        cancel_btn.bind(on_press=self.cancel_edit)

        btn_layout.add_widget(save_btn)
        btn_layout.add_widget(cancel_btn)

        main_layout.add_widget(btn_layout)
        self.add_widget(main_layout)

    def setup_for_add(self):
        self.screen_title.text = 'Add Memo'
        self.current_memo = None
        self.title_input.text = ''
        self.content_input.text = ''
        self.tags_input.text = ''
        self.memo_manager = self.manager.get_screen('list').memo_manager

    def setup_for_edit(self, memo):
        self.screen_title.text = 'Edit Memo'
        self.current_memo = memo
        self.title_input.text = memo['title']
        self.content_input.text = memo['content']
        self.tags_input.text = ', '.join(memo['tags'])
        self.memo_manager = self.manager.get_screen('list').memo_manager

    def save_memo(self, instance):
        title = self.title_input.text.strip()
        content = self.content_input.text.strip()
        tags_text = self.tags_input.text.strip()

        if not title:
            self.show_error('Title is required!')
            return

        tags = [tag.strip() for tag in tags_text.split(',') if tag.strip()] if tags_text else []

        if self.current_memo:
            # Update existing memo
            self.memo_manager.update_memo(self.current_memo['id'], title, content, tags)
        else:
            # Add new memo
            self.memo_manager.add_memo(title, content, tags)

        # Refresh the list and go back
        self.manager.get_screen('list').refresh_memo_list()
        self.manager.current = 'list'

    def cancel_edit(self, instance):
        self.manager.current = 'list'

    def show_error(self, message):
        popup = Popup(title='Error', content=Label(text=message), size_hint=(0.8, 0.4))
        popup.open()


class MemoApp(App):
    def build(self):
        sm = ScreenManager()

        list_screen = MemoListScreen(name='list')
        edit_screen = MemoEditScreen(name='edit')

        sm.add_widget(list_screen)
        sm.add_widget(edit_screen)

        return sm


if __name__ == '__main__':
    MemoApp().run()
