"""
Standalone memo manager - completely independent of any framework
This is a traditional single-node class for local memo management
"""

import json
import os
import time
import uuid
from typing import List, Dict, Optional


class MemoManager:
    """
    Traditional memo manager class - completely free of framework-related code
    Can be used as a standalone local application
    """
    
    def __init__(self, storage_file: str = "memos.json"):
        self.storage_file = storage_file
        self.memos: Dict[str, Dict] = {}
        self._load_memos()
    
    def _load_memos(self):
        """Load memos from storage file"""
        if os.path.exists(self.storage_file):
            try:
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    self.memos = json.load(f)
                print(f"Loaded {len(self.memos)} memos from {self.storage_file}")
            except Exception as e:
                print(f"Error loading memos: {e}")
                self.memos = {}
        else:
            self.memos = {}
    
    def _save_memos(self):
        """Save memos to storage file"""
        try:
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(self.memos, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving memos: {e}")
    
    def add_memo(self, title: str, content: str, tags: List[str] = None) -> str:
        """
        Add new memo
        """
        memo_id = str(uuid.uuid4())
        memo = {
            "id": memo_id,
            "title": title,
            "content": content,
            "tags": tags or [],
            "created_at": time.time(),
            "updated_at": time.time()
        }
        
        self.memos[memo_id] = memo
        self._save_memos()
        
        print(f"Added memo: {title}")
        return memo_id
    
    def get_memo(self, memo_id: str) -> Optional[Dict]:
        """Get memo by ID"""
        return self.memos.get(memo_id)
    
    def get_all_memos(self) -> List[Dict]:
        """Get all memos"""
        return list(self.memos.values())
    
    def update_memo(self, memo_id: str, title: str = None, content: str = None, tags: List[str] = None) -> bool:
        """
        Update existing memo
        """
        if memo_id not in self.memos:
            return False
        
        memo = self.memos[memo_id]
        
        if title is not None:
            memo["title"] = title
        if content is not None:
            memo["content"] = content
        if tags is not None:
            memo["tags"] = tags
        
        memo["updated_at"] = time.time()
        
        self._save_memos()
        
        print(f"Updated memo: {memo['title']}")
        return True
    
    def delete_memo(self, memo_id: str) -> bool:
        """
        Delete memo
        """
        if memo_id not in self.memos:
            return False
        
        memo = self.memos[memo_id]
        del self.memos[memo_id]
        self._save_memos()
        
        print(f"Deleted memo: {memo['title']}")
        return True
    
    def search_memos(self, query: str) -> List[Dict]:
        """Search memos"""
        query_lower = query.lower()
        results = []
        
        for memo in self.memos.values():
            if (query_lower in memo["title"].lower() or 
                query_lower in memo["content"].lower() or
                any(query_lower in tag.lower() for tag in memo["tags"])):
                results.append(memo)
        
        return results
    
    def get_memos_by_tag(self, tag: str) -> List[Dict]:
        """Get memos by tag"""
        results = []
        for memo in self.memos.values():
            if tag in memo["tags"]:
                results.append(memo)
        
        return results
    
    def clear_all_memos(self) -> bool:
        """
        Clear all memos
        """
        self.memos.clear()
        self._save_memos()
        print("Cleared all memos")
        return True


# This class has absolutely no framework-related code!
# It's a pure local memo manager that can be used independently
