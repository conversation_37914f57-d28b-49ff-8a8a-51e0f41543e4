"""
Distributed Cross-platform Memo Application
Integrates with DistributedBus for multi-node synchronization
"""

import os
import sys
import threading
import time
from datetime import datetime
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.scrollview import ScrollView
from kivy.uix.gridlayout import GridLayout
from kivy.uix.popup import Popup
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.clock import Clock

# Add DistributedBus to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from DistributedBus import DistributedBus, service
from memo_manager_standalone import MemoManager as BaseMemoManager


@service(name="MemoService")
class DistributedMemoManager(BaseMemoManager):
    """
    Distributed memo manager that extends the standalone version
    with DistributedBus integration for multi-node synchronization
    """
    
    def __init__(self, storage_file: str = "memos.json"):
        super().__init__(storage_file)
        self.bus = None
        self.sync_callbacks = []
    
    def set_bus(self, bus):
        """Set the DistributedBus instance"""
        self.bus = bus
        # Subscribe to memo events from other nodes
        self.bus.subscribe("memo_added", self._on_remote_memo_added)
        self.bus.subscribe("memo_updated", self._on_remote_memo_updated)
        self.bus.subscribe("memo_deleted", self._on_remote_memo_deleted)
        self.bus.subscribe("memos_cleared", self._on_remote_memos_cleared)
    
    def add_sync_callback(self, callback):
        """Add callback to be called when remote changes occur"""
        self.sync_callbacks.append(callback)
    
    def _notify_sync_callbacks(self):
        """Notify all sync callbacks"""
        for callback in self.sync_callbacks:
            try:
                callback()
            except Exception as e:
                print(f"Error in sync callback: {e}")
    
    def _on_remote_memo_added(self, event_data):
        """Handle memo added from remote node"""
        memo = event_data.get('memo')
        if memo and memo['id'] not in self.memos:
            self.memos[memo['id']] = memo
            self._save_memos()
            print(f"Synced new memo from remote: {memo['title']}")
            self._notify_sync_callbacks()
    
    def _on_remote_memo_updated(self, event_data):
        """Handle memo updated from remote node"""
        memo = event_data.get('memo')
        if memo and memo['id'] in self.memos:
            # Only update if remote version is newer
            local_updated = self.memos[memo['id']].get('updated_at', 0)
            remote_updated = memo.get('updated_at', 0)
            if remote_updated > local_updated:
                self.memos[memo['id']] = memo
                self._save_memos()
                print(f"Synced updated memo from remote: {memo['title']}")
                self._notify_sync_callbacks()
    
    def _on_remote_memo_deleted(self, event_data):
        """Handle memo deleted from remote node"""
        memo_id = event_data.get('memo_id')
        if memo_id and memo_id in self.memos:
            del self.memos[memo_id]
            self._save_memos()
            print(f"Synced deleted memo from remote: {memo_id}")
            self._notify_sync_callbacks()
    
    def _on_remote_memos_cleared(self, event_data):
        """Handle all memos cleared from remote node"""
        self.memos.clear()
        self._save_memos()
        print("Synced clear all memos from remote")
        self._notify_sync_callbacks()


class DistributedMemoListScreen(Screen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.memo_manager = None
        self.bus = None
        self.build_ui()
        self.setup_distributed_system()
    
    def setup_distributed_system(self):
        """Initialize DistributedBus and memo manager"""
        try:
            # Initialize DistributedBus
            self.bus = DistributedBus()
            
            # Create distributed memo manager
            self.memo_manager = DistributedMemoManager()
            self.memo_manager.set_bus(self.bus)
            self.memo_manager.add_sync_callback(self.on_remote_sync)
            
            # Register the service
            self.bus.register_service("MemoService", self.memo_manager)
            
            # Start the bus in a separate thread
            def start_bus():
                try:
                    self.bus.start()
                except Exception as e:
                    print(f"Error starting DistributedBus: {e}")
            
            bus_thread = threading.Thread(target=start_bus, daemon=True)
            bus_thread.start()
            
            # Try to discover and connect to other memo services
            def discover_services():
                time.sleep(2)  # Wait for bus to start
                try:
                    services = self.bus.discover_services("MemoService")
                    print(f"Discovered {len(services)} MemoService instances")
                    for service_info in services:
                        print(f"  - {service_info}")
                except Exception as e:
                    print(f"Error discovering services: {e}")
            
            discovery_thread = threading.Thread(target=discover_services, daemon=True)
            discovery_thread.start()
            
            print("DistributedBus initialized successfully")
            
        except Exception as e:
            print(f"Failed to initialize DistributedBus: {e}")
            # Fallback to standalone mode
            self.memo_manager = BaseMemoManager()
        
        # Initial memo list refresh
        Clock.schedule_once(lambda dt: self.refresh_memo_list(), 1)
    
    def on_remote_sync(self):
        """Called when remote changes are received"""
        # Schedule UI refresh on main thread
        Clock.schedule_once(lambda dt: self.refresh_memo_list(), 0)
    
    def build_ui(self):
        main_layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # Title with sync status
        title_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=50)
        title = Label(text='Distributed Memo App', font_size=20, bold=True)
        self.sync_status = Label(text='🔄 Syncing...', size_hint_x=None, width=100, font_size=12)
        title_layout.add_widget(title)
        title_layout.add_widget(self.sync_status)
        main_layout.add_widget(title_layout)
        
        # Search bar
        search_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=40)
        self.search_input = TextInput(hint_text='Search memos...', multiline=False)
        search_btn = Button(text='Search', size_hint_x=None, width=80)
        search_btn.bind(on_press=self.search_memos)
        search_layout.add_widget(self.search_input)
        search_layout.add_widget(search_btn)
        main_layout.add_widget(search_layout)
        
        # Memo list
        self.memo_scroll = ScrollView()
        self.memo_list = GridLayout(cols=1, size_hint_y=None, spacing=5)
        self.memo_list.bind(minimum_height=self.memo_list.setter('height'))
        self.memo_scroll.add_widget(self.memo_list)
        main_layout.add_widget(self.memo_scroll)
        
        # Buttons
        button_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=50, spacing=10)
        
        add_btn = Button(text='Add Memo')
        add_btn.bind(on_press=self.show_add_memo)
        
        refresh_btn = Button(text='Refresh')
        refresh_btn.bind(on_press=lambda x: self.refresh_memo_list())
        
        sync_btn = Button(text='Force Sync')
        sync_btn.bind(on_press=self.force_sync)
        
        clear_btn = Button(text='Clear All')
        clear_btn.bind(on_press=self.clear_all_memos)
        
        button_layout.add_widget(add_btn)
        button_layout.add_widget(refresh_btn)
        button_layout.add_widget(sync_btn)
        button_layout.add_widget(clear_btn)
        
        main_layout.add_widget(button_layout)
        self.add_widget(main_layout)
    
    def force_sync(self, instance):
        """Force synchronization with other nodes"""
        if self.bus:
            try:
                # Rediscover services
                services = self.bus.discover_services("MemoService")
                self.sync_status.text = f'📡 {len(services)} nodes'
                print(f"Force sync: found {len(services)} MemoService nodes")
            except Exception as e:
                print(f"Error during force sync: {e}")
                self.sync_status.text = '❌ Sync error'
        else:
            self.sync_status.text = '📱 Local only'
    
    def refresh_memo_list(self):
        if not self.memo_manager:
            return
            
        self.memo_list.clear_widgets()
        memos = self.memo_manager.get_all_memos()
        
        if not memos:
            no_memos = Label(text='No memos yet. Tap "Add Memo" to create one!', 
                           size_hint_y=None, height=50)
            self.memo_list.add_widget(no_memos)
            return
        
        # Sort by updated_at descending
        memos.sort(key=lambda x: x.get('updated_at', 0), reverse=True)
        
        for memo in memos:
            memo_widget = self.create_memo_widget(memo)
            self.memo_list.add_widget(memo_widget)
        
        # Update sync status
        if self.bus:
            try:
                services = self.bus.discover_services("MemoService")
                self.sync_status.text = f'📡 {len(services)} nodes'
            except:
                self.sync_status.text = '🔄 Syncing...'
        else:
            self.sync_status.text = '📱 Local only'
    
    def create_memo_widget(self, memo):
        memo_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=120, 
                               padding=5, spacing=5)
        
        # Title and date
        header_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=30)
        title_label = Label(text=memo['title'], font_size=16, bold=True, 
                          text_size=(None, None), halign='left')
        
        # Format date
        date_str = datetime.fromtimestamp(memo['updated_at']).strftime('%m/%d %H:%M')
        date_label = Label(text=date_str, size_hint_x=None, width=80, font_size=12)
        
        header_layout.add_widget(title_label)
        header_layout.add_widget(date_label)
        memo_layout.add_widget(header_layout)
        
        # Content preview
        content_preview = memo['content'][:100] + ('...' if len(memo['content']) > 100 else '')
        content_label = Label(text=content_preview, text_size=(None, None), 
                            font_size=12, halign='left')
        memo_layout.add_widget(content_label)
        
        # Buttons
        btn_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=30, spacing=5)
        
        edit_btn = Button(text='Edit', size_hint_x=None, width=60)
        edit_btn.bind(on_press=lambda x, m=memo: self.show_edit_memo(m))
        
        delete_btn = Button(text='Delete', size_hint_x=None, width=60)
        delete_btn.bind(on_press=lambda x, m=memo: self.delete_memo(m))
        
        btn_layout.add_widget(edit_btn)
        btn_layout.add_widget(delete_btn)
        btn_layout.add_widget(Label())  # Spacer
        
        memo_layout.add_widget(btn_layout)
        
        return memo_layout
    
    def search_memos(self, instance):
        if not self.memo_manager:
            return
            
        query = self.search_input.text.strip()
        if query:
            memos = self.memo_manager.search_memos(query)
        else:
            memos = self.memo_manager.get_all_memos()
        
        self.memo_list.clear_widgets()
        
        if not memos:
            no_results = Label(text='No memos found.', size_hint_y=None, height=50)
            self.memo_list.add_widget(no_results)
            return
        
        memos.sort(key=lambda x: x.get('updated_at', 0), reverse=True)
        for memo in memos:
            memo_widget = self.create_memo_widget(memo)
            self.memo_list.add_widget(memo_widget)
    
    def show_add_memo(self, instance):
        self.manager.get_screen('edit').setup_for_add()
        self.manager.current = 'edit'
    
    def show_edit_memo(self, memo):
        self.manager.get_screen('edit').setup_for_edit(memo)
        self.manager.current = 'edit'
    
    def delete_memo(self, memo):
        if self.memo_manager:
            self.memo_manager.delete_memo(memo['id'])
            self.refresh_memo_list()
    
    def clear_all_memos(self, instance):
        popup_layout = BoxLayout(orientation='vertical', spacing=10)
        popup_layout.add_widget(Label(text='Are you sure you want to clear all memos?\nThis will sync to all connected devices!'))
        
        btn_layout = BoxLayout(orientation='horizontal', spacing=10, size_hint_y=None, height=40)
        yes_btn = Button(text='Yes')
        no_btn = Button(text='No')
        
        popup = Popup(title='Confirm Clear All', content=popup_layout, size_hint=(0.8, 0.4))
        
        yes_btn.bind(on_press=lambda x: self.confirm_clear_all(popup))
        no_btn.bind(on_press=popup.dismiss)
        
        btn_layout.add_widget(yes_btn)
        btn_layout.add_widget(no_btn)
        popup_layout.add_widget(btn_layout)
        
        popup.open()
    
    def confirm_clear_all(self, popup):
        if self.memo_manager:
            self.memo_manager.clear_all_memos()
            self.refresh_memo_list()
        popup.dismiss()


class DistributedMemoEditScreen(Screen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.memo_manager = None
        self.current_memo = None
        self.build_ui()
    
    def build_ui(self):
        main_layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # Title
        self.screen_title = Label(text='Add Memo', size_hint_y=None, height=50, font_size=20)
        main_layout.add_widget(self.screen_title)
        
        # Title input
        main_layout.add_widget(Label(text='Title:', size_hint_y=None, height=30, halign='left'))
        self.title_input = TextInput(multiline=False, size_hint_y=None, height=40)
        main_layout.add_widget(self.title_input)
        
        # Content input
        main_layout.add_widget(Label(text='Content:', size_hint_y=None, height=30, halign='left'))
        self.content_input = TextInput(multiline=True)
        main_layout.add_widget(self.content_input)
        
        # Tags input
        main_layout.add_widget(Label(text='Tags (comma separated):', size_hint_y=None, height=30, halign='left'))
        self.tags_input = TextInput(multiline=False, size_hint_y=None, height=40)
        main_layout.add_widget(self.tags_input)
        
        # Buttons
        btn_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=50, spacing=10)
        
        save_btn = Button(text='Save & Sync')
        save_btn.bind(on_press=self.save_memo)
        
        cancel_btn = Button(text='Cancel')
        cancel_btn.bind(on_press=self.cancel_edit)
        
        btn_layout.add_widget(save_btn)
        btn_layout.add_widget(cancel_btn)
        
        main_layout.add_widget(btn_layout)
        self.add_widget(main_layout)
    
    def setup_for_add(self):
        self.screen_title.text = 'Add Memo'
        self.current_memo = None
        self.title_input.text = ''
        self.content_input.text = ''
        self.tags_input.text = ''
        self.memo_manager = self.manager.get_screen('list').memo_manager
    
    def setup_for_edit(self, memo):
        self.screen_title.text = 'Edit Memo'
        self.current_memo = memo
        self.title_input.text = memo['title']
        self.content_input.text = memo['content']
        self.tags_input.text = ', '.join(memo['tags'])
        self.memo_manager = self.manager.get_screen('list').memo_manager
    
    def save_memo(self, instance):
        if not self.memo_manager:
            self.show_error('Memo manager not initialized!')
            return
            
        title = self.title_input.text.strip()
        content = self.content_input.text.strip()
        tags_text = self.tags_input.text.strip()
        
        if not title:
            self.show_error('Title is required!')
            return
        
        tags = [tag.strip() for tag in tags_text.split(',') if tag.strip()] if tags_text else []
        
        if self.current_memo:
            # Update existing memo
            self.memo_manager.update_memo(self.current_memo['id'], title, content, tags)
        else:
            # Add new memo
            self.memo_manager.add_memo(title, content, tags)
        
        # Refresh the list and go back
        self.manager.get_screen('list').refresh_memo_list()
        self.manager.current = 'list'
    
    def cancel_edit(self, instance):
        self.manager.current = 'list'
    
    def show_error(self, message):
        popup = Popup(title='Error', content=Label(text=message), size_hint=(0.8, 0.4))
        popup.open()


class DistributedMemoApp(App):
    def build(self):
        sm = ScreenManager()
        
        list_screen = DistributedMemoListScreen(name='list')
        edit_screen = DistributedMemoEditScreen(name='edit')
        
        sm.add_widget(list_screen)
        sm.add_widget(edit_screen)
        
        return sm


if __name__ == '__main__':
    DistributedMemoApp().run()
