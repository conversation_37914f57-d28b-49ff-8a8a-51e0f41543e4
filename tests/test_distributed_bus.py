#!/usr/bin/env python3
"""
Unit tests for the DistributedBus framework.
"""

import unittest
import time
import threading
import tempfile
import shutil
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from DistributedBus import DistributedBus, service, event_subscriber


@service(name="TestService")
class TestService:
    """Test service for unit tests."""
    
    def __init__(self):
        self.counter = 0
        self.data = {}
    
    def increment(self):
        """Increment counter."""
        self.counter += 1
        return self.counter
    
    def get_counter(self):
        """Get current counter value."""
        return self.counter
    
    def set_data(self, key, value):
        """Set data."""
        self.data[key] = value
        return True
    
    def get_data(self, key):
        """Get data."""
        return self.data.get(key)
    
    def echo(self, message):
        """Echo a message."""
        return f"Echo: {message}"


class TestDistributedBus(unittest.TestCase):
    """Test cases for DistributedBus."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for CA
        self.temp_dir = tempfile.mkdtemp()
        self.bus = None
    
    def tearDown(self):
        """Clean up test environment."""
        if self.bus:
            self.bus.close()
        
        # Clean up temporary directory
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_bus_start_stop(self):
        """Test starting and stopping the bus."""
        self.bus = DistributedBus(ca_dir=self.temp_dir)
        
        # Bus should not be started initially
        self.assertFalse(self.bus._started)
        
        # Start the bus
        result = self.bus.start()
        self.assertTrue(self.bus._started)
        self.assertEqual(result, self.bus)  # Should return self
        
        # Starting again should be safe
        result2 = self.bus.start()
        self.assertEqual(result2, self.bus)
        
        # Stop the bus
        self.bus.close()
        self.assertFalse(self.bus._started)
    
    def test_service_registration_and_lookup(self):
        """Test service registration and lookup."""
        self.bus = DistributedBus(ca_dir=self.temp_dir).start()
        
        # TestService should be auto-registered due to @service decorator
        service = self.bus.lookup("TestService", timeout=2.0)
        self.assertIsNotNone(service)
        
        # Test service methods
        result = service.echo("Hello")
        self.assertEqual(result, "Echo: Hello")
        
        counter1 = service.increment()
        self.assertEqual(counter1, 1)
        
        counter2 = service.get_counter()
        self.assertEqual(counter2, 1)
    
    def test_service_not_found(self):
        """Test lookup of non-existent service."""
        self.bus = DistributedBus(ca_dir=self.temp_dir).start()
        
        service = self.bus.lookup("NonExistentService", timeout=1.0)
        self.assertIsNone(service)
    
    def test_manual_service_registration(self):
        """Test manual service registration."""
        self.bus = DistributedBus(ca_dir=self.temp_dir).start()
        
        # Create and register service manually
        manual_service = TestService()
        self.bus.register_service("ManualTestService", manual_service)
        
        # Look up the manually registered service
        service = self.bus.lookup("ManualTestService", timeout=2.0)
        self.assertIsNotNone(service)
        
        # Test that it works
        result = service.echo("Manual")
        self.assertEqual(result, "Echo: Manual")
    
    def test_event_publishing_and_subscribing(self):
        """Test event publishing and subscribing."""
        self.bus = DistributedBus(ca_dir=self.temp_dir).start()
        
        # Set up event tracking
        received_events = []
        
        def event_handler(event):
            received_events.append(event)
        
        # Subscribe to event
        self.bus.subscribe("test_event", event_handler)
        
        # Publish event
        test_data = {"message": "test", "number": 42}
        self.bus.publish_event("test_event", test_data)
        
        # Wait for event to be processed
        time.sleep(0.5)
        
        # Check that event was received
        self.assertEqual(len(received_events), 1)
        event = received_events[0]
        self.assertEqual(event.event_name, "test_event")
        self.assertEqual(event.data, test_data)
        self.assertIsInstance(event.timestamp, float)
    
    def test_multiple_event_subscribers(self):
        """Test multiple subscribers for the same event."""
        self.bus = DistributedBus(ca_dir=self.temp_dir).start()
        
        # Set up multiple event handlers
        received_events_1 = []
        received_events_2 = []
        
        def handler1(event):
            received_events_1.append(event)
        
        def handler2(event):
            received_events_2.append(event)
        
        # Subscribe both handlers
        self.bus.subscribe("multi_event", handler1)
        self.bus.subscribe("multi_event", handler2)
        
        # Publish event
        self.bus.publish_event("multi_event", {"test": "data"})
        
        # Wait for events to be processed
        time.sleep(0.5)
        
        # Both handlers should have received the event
        self.assertEqual(len(received_events_1), 1)
        self.assertEqual(len(received_events_2), 1)
    
    def test_context_manager(self):
        """Test using DistributedBus as context manager."""
        with DistributedBus(ca_dir=self.temp_dir).start() as bus:
            self.assertTrue(bus._started)
            
            # Should be able to use the bus
            service = bus.lookup("TestService", timeout=2.0)
            self.assertIsNotNone(service)
        
        # Bus should be closed after context
        self.assertFalse(bus._started)
    
    def test_node_info(self):
        """Test getting node information."""
        self.bus = DistributedBus(ca_dir=self.temp_dir).start()
        
        node_info = self.bus.get_node_info()
        
        self.assertIn("ca_fingerprint", node_info)
        self.assertIn("started", node_info)
        self.assertIn("local_services", node_info)
        self.assertIn("discovered_services", node_info)
        
        self.assertTrue(node_info["started"])
        self.assertIn("TestService", node_info["local_services"])
    
    def test_wait_for_service(self):
        """Test waiting for a service to become available."""
        self.bus = DistributedBus(ca_dir=self.temp_dir).start()
        
        # TestService should be immediately available
        service = self.bus.wait_for_service("TestService", timeout=5.0)
        self.assertIsNotNone(service)
        
        # Non-existent service should timeout
        service = self.bus.wait_for_service("NonExistentService", timeout=1.0)
        self.assertIsNone(service)


class TestEventSubscriberDecorator(unittest.TestCase):
    """Test the @event_subscriber decorator."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.received_events = []
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_decorator_registration(self):
        """Test that @event_subscriber decorator registers handlers."""
        
        @event_subscriber("decorator_test")
        def test_handler(event):
            self.received_events.append(event)
        
        # Start bus (this should register decorated handlers)
        with DistributedBus(ca_dir=self.temp_dir).start() as bus:
            # Publish event
            bus.publish_event("decorator_test", {"decorator": "test"})
            
            # Wait for event processing
            time.sleep(0.5)
        
        # Handler should have been called
        self.assertEqual(len(self.received_events), 1)
        self.assertEqual(self.received_events[0].data["decorator"], "test")


if __name__ == "__main__":
    unittest.main()
